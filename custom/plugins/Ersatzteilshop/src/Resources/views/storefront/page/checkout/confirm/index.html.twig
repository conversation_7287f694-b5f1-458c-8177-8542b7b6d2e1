{% sw_extends '@Storefront/storefront/page/checkout/confirm/index.html.twig' %}

{% block base_head %}
    {% sw_include '@Storefront/storefront/page/checkout/cart/meta.html.twig' %}
{% endblock %}

{% block base_header %}
    {% sw_include '@Storefront/storefront/layout/header/header-minimal-checkout.html.twig' %}
{% endblock %}

{% block page_checkout_confirm %}
    {{ block('page_checkout_confirm_alerts') }}
    {{ block('page_checkout_confirm_header') }}

    <div data-confirm-payment-toggle="true">
        {% block page_checkout_confirm_payment %}
            <div class="checkout-confirm-step-one">
                {% sw_include '@Storefront/storefront/page/checkout/confirm/confirm-payment.html.twig' %}

{#                <div class="register-submit k11-mt-40">#}
{#                    <a href="{{ path('frontend.checkout.cart.page') }}" class="btn button_fix checkout-register-back">#}
{#                        {{ "checkout.returnToCart"|trans|sw_sanitize }}#}
{#                    </a>#}
{#                    <button id="confirm-payment-next" type="submit" class="btn button_fix checkout-register-submit">#}
{#                        {{ "account.paymentSubmit"|trans|sw_sanitize }}#}
{#                    </button>#}
{#                </div>#}
            </div>
        {% endblock %}

        <div class="checkout-confirm-step-two">
            {{ block('page_checkout_confirm_product_table') }}
            {{ block('page_checkout_confirm_address') }}
            {{ block('page_checkout_confirm_hidden_line_items_information') }}
{#            {{ block('page_checkout_confirm_tos') }}#}
{#            {{ block('page_checkout_confirm_order_bottom') }}#}
{#            {{ block('page_checkout_aside') }}#}
{#            {{ block('page_checkout_confirm_tos_control_label') }}#}


        </div>
    </div>
{#    {{ block('page_checkout_confirm_address') }}#}
{#    {{ block('page_checkout_confirm_product_table') }}#}
{#    {{ block('page_checkout_confirm_hidden_line_items_information') }}#}
{% endblock %}

{% block page_checkout_confirm_alerts %}
    {% block page_checkout_confirm_violations %}
        {% for violation in formViolations.getViolations() %}
            {% set snippetName = "error.#{violation.code}" %}
            {% set fieldName = violation.propertyPath|trim('/', 'left') %}

            {% sw_include '@Storefront/storefront/utilities/alert.html.twig' with {
                type: "danger",
                content: snippetName|trans({'%field%': fieldName})|sw_sanitize
            } %}
        {% endfor %}
    {% endblock %}
    {% block page_checkout_confirm_errors %}
        <div class="unzer-payment--error-wrapper" hidden="hidden">
            {% sw_include '@Storefront/storefront/utilities/alert.html.twig' with {
                type: "danger",
                content: ""
            } %}
        </div>

    {% endblock %}
{% endblock %}

{% block page_checkout_confirm_header %}
{#        {% block checkout_progress_bar %}#}
{#            {% set progress_step = 2 %}#}
{#            <div id="progressStep" data-step="{{ progress_step }}">#}
{#                {% sw_include '@Storefront/storefront/page/checkout/checkout-progress-bar.html.twig' %}#}
{#            </div>#}
{#        {% endblock %}#}



{#    <h1 class="confirm-main-header">#}
{#        {{ "checkout.confirmHeader"|trans|sw_sanitize }}#}
{#    </h1>#}
{#    <p>{{ "checkout.confirmHeaderNote"|trans|sw_sanitize }}</p>#}
{% endblock %}



{% block page_checkout_confirm_product_table %}
{#    <h2 class="confirm-section-title k11-mt-40">{{ "checkout.confirmTitle"|trans|sw_sanitize }}</h2>#}

    <div class="confirm-product k11-mt-40">
        {% block page_checkout_confirm_table_container %}
            <h2 class="confirm-section-title">{{ 'checkout.confirmProduct'|trans|sw_sanitize }}</h2>
            <div class="card">
                <div class="card-body">
{#                    {% block page_checkout_confirm_table_header %}#}
{#                        {% sw_include '@Storefront/storefront/page/checkout/confirm/confirm-product-header.html.twig' %}#}
{#                    {% endblock %}#}

                    {% block page_checkout_confirm_table_header %}
                        {% sw_include '@Storefront/storefront/page/checkout/cart/cart-product-header.html.twig' %}
                    {% endblock %}

                    {% block page_checkout_confirm_table_items %}
                        {% for lineItem in page.cart.lineItems %}
                            {% if not isTip(lineItem) %}
                                {% block page_checkout_confirm_table_item %}
                                    {% sw_include '@Storefront/storefront/page/checkout/confirm/confirm-item.html.twig' %}
                                {% endblock %}
                            {% endif %}
                        {% endfor %}
                    {% endblock %}
                </div>
            </div>
        {% endblock %}
    </div>
{% endblock %}


{% block page_checkout_confirm_address %}
    {# Adress-Container über die ganze Breite #}
    <h2 class="confirm-section-title">Adresse prüfen</h2>
    <div class="confirm-address">
        {% sw_include '@Storefront/storefront/page/checkout/confirm/confirm-address.html.twig' %}
    </div>

    {# Payment und Voucher Container mit korrekter Struktur #}
    <div class="confirm-payment-voucher-wrapper">
        <div class="confirm-payment-wrapper">
            <h2 class="section-title-outside">Zahlungsart prüfen</h2>
            <div class="confirm-payment-container">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        {{ "checkout.paymentHeading"|trans|sw_sanitize }}
                    </h3>
                    <img id="svg-back-to-payment" alt="edit_note" style="position: relative; top: -10px; cursor: pointer" src="{{ asset('bundles/ersatzteilshop/assets/icons/svgs/edit_note.svg') }}">
{#                    <svg id="svg-back-to-payment" style="position: relative; top: -10px; cursor: pointer" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">#}
{#                        <mask id="mask0_6955_19817" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">#}
{#                            <rect width="24" height="24" fill="#D9D9D9"/>#}
{#                        </mask>#}
{#                        <g mask="url(#mask0_6955_19817)">#}
{#                            <path d="M4 14V12H11V14H4ZM4 10V8H15V10H4ZM4 6V4H15V6H4ZM13 20V16.925L18.525 11.425C18.675 11.275 18.8417 11.1667 19.025 11.1C19.2083 11.0333 19.3917 11 19.575 11C19.775 11 19.9667 11.0375 20.15 11.1125C20.3333 11.1875 20.5 11.3 20.65 11.45L21.575 12.375C21.7083 12.525 21.8125 12.6917 21.8875 12.875C21.9625 13.0583 22 13.2417 22 13.425C22 13.6083 21.9667 13.7958 21.9 13.9875C21.8333 14.1792 21.725 14.35 21.575 14.5L16.075 20H13ZM19.575 14.4L20.5 13.425L19.575 12.5L18.625 13.45L19.575 14.4Z" fill="#202E3D"/>#}
{#                        </g>#}
{#                    </svg>#}
                </div>

                {% set selectedPayment = context.paymentMethod %}

                <div class="selected-payment-method k11-mt-20 d-flex align-items-center gap-2">
                    {% if selectedPayment.media %}
                        <img src="{{ selectedPayment.media.url }}"
                             alt="{{ selectedPayment.media.translated.alt ?: selectedPayment.translated.name }}"
                             title="{{ selectedPayment.media.translated.title ?: selectedPayment.translated.name }}"
                             class="payment-method-image"
                             style="max-width: 50px; height: auto;" />
                    {% endif %}

                    <span class="payment-method-name">
                        {{ selectedPayment.translated.name }}
                    </span>
                </div>
            </div>
        </div>

        <div class="confirm-voucher-wrapper">
            <h2 class="section-title-outside">Gutschein einlösen</h2>
            <div class="confirm-voucher-container">
                {% block page_checkout_vouchcode %}
                    <div class="confirm-voucher">
                        <div class="card-body">
                            {% sw_include '@Storefront/storefront/page/checkout/confirm/confirm-voucher.html.twig' %}
                        </div>
                    </div>
                {% endblock %}
            </div>
        </div>
    </div>

{#    {% if tipProductId %}#}
        <div class="tip-container k11-md-p-20 k11-p-10 k11-mb-20 k11-mt-40" data-tipping="true">
            {% set tip = 0 %}
            {% set total = page.cart.price.totalPrice %}
            {% for lineItem in page.cart.lineItems %}
                {% if isTip(lineItem) %}
                    {% set tip = lineItem.price.totalPrice %}
                    {% set total = total - tip %}
                {% endif %}
            {% endfor %}
            <label class="k11-checkbox-container k11-gap-10">
                <input type="checkbox" class="js-tip-checkbox" {% if tip %}checked{% endif %}>
                <span class="k11-checkbox"></span>
                <div>
                    <div class="prio-label">Dankeschön an das Team</div>
                    <div>Mehr als nur Ersatzteile</div>
                </div>
            </label>
            <div class="tip-grid">
                <span class="tip-item">Reparaturvideos</span>
                <span class="tip-item">Fehleranalyse</span>
                <span class="tip-item">Technikerforum</span>
                <span class="tip-item">WhatsApp</span>
                <span class="tip-item">Telefonsupport</span>
                <span class="tip-item">YouTube</span>
            </div>
            <div class="tip-amount-container">
                {% set hit = false %}
                {% set formAjaxSubmitOptions = {
                    reloadOnFinish: true
                } %}
                {% for percentage in [2, 4, 6] %}
                    <form method="POST" action="{{ path('frontend.checkout.tip') }}" data-form-csrf-handler="true" data-form-ajax-submit="true" data-form-ajax-submit-options='{{ formAjaxSubmitOptions|json_encode }}'>
                        {{ sw_csrf('frontend.checkout.tip') }}
                        <input type="hidden" name="amount" value="{{ (total * percentage/100)|round(2) }}">
                        <input type="hidden" name="redirectTo" value="frontend.checkout.confirm.page">
                        <div role="button" class="tip-amount js-tip-amount
                        {% if (tip - ((total * percentage/100)|round(2)))|abs < 0.01 %}
                            {% set hit = true %}
                            tip-selected
                        {% endif %}">
                            <span class="tip-amount-percentage">{{ percentage }}%</span>
                            <span class="tip-amount-absolute">{{ (total * percentage/100)|round(2)|currency }}</span>
                        </div>
                    </form>
                {% endfor %}
                <form method="POST" action="{{ path('frontend.checkout.tip') }}" class="tip-amount-custom js-tip-amount-custom{% if not hit and tip %} tip-selected{% endif %}" data-form-csrf-handler="true" data-form-ajax-submit="true" data-form-ajax-submit-options='{{ formAjaxSubmitOptions|json_encode }}'>
                    {{ sw_csrf('frontend.checkout.tip') }}
                    <input type="hidden" name="redirectTo" value="frontend.checkout.confirm.page">
                    <input name="amount" type="text" autocomplete="off" placeholder="Eigener Betrag 0,00" {% if not hit and tip %}value="{{ tip|number_format(2, ',', '.') }}"{% endif %}>
                    <div class="suffix">&euro;</div>
                </form>
            </div>
        </div>
{#    {% endif %}#}
{% endblock %}

{#        {% block page_checkout_confirm_customer_comment_control %}#}
{#            <div class="checkout-customer-comment-control">#}
{#                {% block page_checkout_confirm_customer_comment_control_textfield %}#}
{#                    <div class="d-flex k11-gap-10 k11-mb-15">#}
{#                        <div class="d-none d-md-block">#}
{#                            <img width="100px" height="100px"#}
{#                                 src="{{ asset('bundles/ersatzteilshop/assets/icons/icon_information.png') }}"#}
{#                                 alt="information_icon" class="appliance-info--icon">#}
{#                        </div>#}
{#                        <div class="k11-border k11-p-15">#}
{#                            {{ 'checkout.customerNotice'|trans|raw }}#}
{#                        </div>#}
{#                    </div>#}
{#                    <textarea class="form-control"#}
{#                              placeholder="{{ "checkout.customerCommentPlaceholder"|trans|sw_sanitize }}"#}
{#                              id="{{ constant('Shopware\\Core\\Checkout\\Order\\SalesChannel\\OrderService::CUSTOMER_COMMENT_KEY') }}"#}
{#                              form="confirmOrderForm"#}
{#                              name="{{ constant('Shopware\\Core\\Checkout\\Order\\SalesChannel\\OrderService::CUSTOMER_COMMENT_KEY') }}"></textarea>#}
{#                {% endblock %}#}
{#            </div>#}
{#        {% endblock %}#}

{% block base_footer %}
    <footer class="footer-main">
        {% block base_footer_inner %}
            {% sw_include '@Storefront/storefront/layout/footer/scroll_up_bar.html.twig' %}
            {% sw_include '@Storefront/storefront/layout/footer/footer.html.twig' %}
        {% endblock %}
    </footer>

{% endblock %}

{#{% block page_checkout_confirm_customer_comment_control_textfield_label %}#}
{#{% endblock %}#}

{% block page_checkout_additional %}
{% endblock %}

{% block page_checkout_aside_actions %}{% endblock %}

 {% block page_checkout_confirm_shipping %}
     <div class="col-sm-6 confirm-shipping">
         {% sw_include '@Storefront/storefront/page/checkout/confirm/confirm-shipping.html.twig' %}
     </div>
 {% endblock %}


{% block page_checkout_aside %}
<div class="checkout-main">
    <div class="page-checkout-aside">

        {{ parent() }}


            {#        {% block page_checkout_delivery_delay %} #}
            {#            <div class="confirm-tos" style="display: flex;-ms-flex-align: center;align-items: center;padding-top: 40px;padding-bottom: 20px;gap: 20px;"> #}
            {#                <div class="d-none d-md-flex" style="min-width: 100px; height: 100px; border-radius: 8px; background: linear-gradient(180deg, #FDBB33 0%, #FDBB33 100%); display: flex; justify-content: center; align-items: center;"> #}
            {#                    <img height="84" width="84" src="{{ asset('bundles/ersatzteilshop/assets/icons/delay_warning.png') }}" title="Delay Warning Icon" alt="Delay Warning Icon"/> #}
            {#                </div> #}
            {#                <div class="" style="width: 100%; padding: 16px; border-radius: 8px;border: 2px solid #FDBB33;background: #FFF;"> #}
            {#                    <span style="font-size: 16px; font-style: normal; font-weight: 900;"> #}
            {#                        Hinweis: #}
            {#                    </span> #}
            {#                    <br> #}
            {#                    <span>Aufgrund von bundesweiten Protestaktionen verschiedener Berufsgruppen kann es aktuell zu Lieferverzögerungen kommen. <br> Wir bitten um Ihr Verständnis und danken im voraus für Ihre Geduld.</span> #}
            {#                </div> #}
            {#            </div> #}
            {#        {% endblock %} #}

            {{ block('page_checkout_confirm_tos') }}


        {% block page_checkout_confirm_order_bottom %}
            <div class="checkout-action text-center">
                <div class="checkout-buttons">

                    {% block page_checkout_confirm_order_form %}
                        <form id="confirmOrderForm"
                              action="{{ path('frontend.checkout.finish.order') }}"
                              data-form-csrf-handler="true"
                              data-form-preserver="true"
                              data-form-submit-loader="true"
                              method="post">

                            {% block page_checkout_aside_actions_csrf %}
                                {{ sw_csrf('frontend.checkout.finish.order') }}
                            {% endblock %}

{#                            <a href="{{ path('frontend.checkout.cart.page') }}"#}
{#                               class="btn btn-secondary button_fix checkout-register-back mt-3 mt-md-0">#}
{#                                {{ "checkout.returnToCart"|trans|sw_sanitize }}#}
{#                            </a>#}

{#                            <a href="#"#}
{#                               id="back-to-payment"#}
{#                               class="btn btn-secondary button_fix checkout-register-back mt-3 mt-md-0">#}
{#                                Zurück zur Zahlungsart#}
{#                            </a>#}
                            <a href="{{ path('frontend.checkout.cart.page') }}" class="btn button_fix checkout-register-back hover-light-grey k11-mr-10">
                                {{ "checkout.returnToCart"|trans|sw_sanitize }}
                            </a>

                            {% block page_checkout_confirm_form_submit %}
                                <button id="confirmFormSubmit"
                                        class="btn btn-primary button_fix float-right checkout-register-submit hover-light-green"
                                        form="confirmOrderForm"
                                    {% if page.cart.errors|length is same as(1) %}
                                        {% for error in page.cart.errors %}
                                            {% if _key != "promotion-not-found" %}
                                                disabled
                                            {% endif %}
                                        {% endfor %}
                                    {% endif %}
                                    {% if page.cart.errors|length > 1 %}
                                        disabled
                                    {% endif %}
                                        type="submit">
                                    {{ "checkout.confirmSubmit"|trans|sw_sanitize }}
                                </button>

                                {% sw_include '@SwagPayPal/storefront/component/ecs-spb-checkout/ecs-spb-data.html.twig' %}
                            {% endblock %}
                        </form>
                    {% endblock %}
                </div>
            </div>
        {% endblock %}
    </div>
</div>
{% endblock %}

{% block page_checkout_confirm_tos_header %}{% endblock %}


{% block page_checkout_confirm_tos_control_label %}
    <label for="tos"
           class="checkout-confirm-tos-label">
        {% block page_checkout_confirm_revocation_notice_link %}
            <a href="{{ '/widerrufsrecht.html'|trans }}" target="_blank">
                {{ 'ersatzteilshop.page.revocation'|trans }}
            </a><br>
        {% endblock %}
        {{ "ersatzteilshop.page.agb"|trans|raw }}
    </label>
{% endblock %}


