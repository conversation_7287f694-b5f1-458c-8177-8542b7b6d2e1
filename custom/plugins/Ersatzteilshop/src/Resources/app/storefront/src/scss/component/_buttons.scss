.editCheckout_button {
  background-color: transparent;
  color: #44b436;
  font-weight: 500;
  text-align: right;
  border: none;
  &:active {
    border-color: white;
  }
  &:focus {
    outline: none;
  }
  &:hover {
    color: #44b436;
  }
}
.btn-disabled {
  border: 1px solid #999999 !important;
  background-color: #cccccc !important;
  color: #666666 !important;
}

.edit_icon {
  background-color: #44b436;
  color: #44b436;
  &:hover {
    background-color: #44b436;
  }
}

.add-to-cart__button {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  width: 170px;
  height: 42px;
  background-color: #202E3D;
  padding: 8px 5px;
  border: none;

  .information__text {
    margin-left: 8px;
    font-size: 14px;
    font-weight: 900;
    color: #fff;
  }

  svg {
    path {
      fill: #fff;
    }
  }

  &:focus {
    outline: none;
    box-shadow: none;
  }

  &:hover {
    background-color: #74cb7b !important;

    .information__text {
      color: #202E3D;
    }

    svg {
      path {
        fill: #202E3D;
      }
    }
  }
}

.notify__link {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  width: 206px;
  height: 42px;
  background-color: #fff;
  padding: 8px 5px;
  border: 2px solid #202E3D;
  margin-bottom: 10px;
  @include media-breakpoint-down(sm) {
    width: 100%;
  }
  .information__text {
    margin-left: 8px;
    font-size: 14px;
    font-weight: 900;
    color: #202E3D;
  }
  &:focus {
    outline: none;
  }
}

.notify__button {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  width: 100%;
  gap: 7px;
  background-color: #fff;
  padding: 8px 5px;
  border: 2px solid #202E3D;
  margin-bottom: 10px;
  margin-top: 20px;
  .information__text {
    font-size: 14px;
    font-weight: 900;
    color: #202E3D;
  }
  &:focus {
    outline: none;
  }
}


.address-editor-create, .btn-green {
  color: #fff;
  background-color: #44b438;
  border-color: #44b438;

  &:hover {
    color: #fff;
    background-color: #39972f;
    border-color: #358d2c;
  }

  &:not(.collapsed) {
    color: #44b438;
    background-color: #fbfbfb;
    border-color: #44b438;

    &:hover {
      color: #44b438;
      background-color: #fbfbfb;
    }
  }
}
.btn-outline-primary:not(:disabled):not(.disabled):active, .btn-outline-primary:not(:disabled):not(.disabled).active, .show > .btn-outline-primary.dropdown-toggle {
  color: #fff;
  background-color: #39972f;
  border-color: #358d2c;
}

.btn-secondary {
  color: #000;
  background-color: #eee;
  border: 1px solid #666;
  border-radius: 4px;
  padding: 3px 10px;
  cursor: pointer;

  &:hover {
    background-color: #fbfbfb;
  }
}

a.button {
  display: inline-block;
  text-decoration: none;
}

.button_fix {
  min-width: 200px;
  width: 200px;
  max-width: 100%;
  text-align: center;
}

.button {
  background-color: #44b436;
  color: #fff;
  border: 0;
  border-radius: 4px;
  padding: 4px 10px;
  cursor: pointer;

  &:hover {
    background-color: #4dc53e;
    color: #fff;
  }
  .vouchInput {

  }
}

.button_100 {
  margin-top: 1rem;
  margin-bottom: 1rem;
  display: block;
  width: 100%;
}

.close-search-modal-button {
  display: none;
}

@include media-breakpoint-down(sm) {
  .btn-block {
    display: block;
    width: 60%;
  }

  .button-sm-full {
    width: 100%;
  }

  .close-search-modal-button {
    margin-top: 20px;
    width: 100%;
    display:block;
  }
}

.hover-light-green:hover {
    background-color: #93ED9A !important;
}

.checkout-button-grey {
    border: none;
    border-radius: 8px;
    background-color: #E0E0E0;
    font-size: 16px;
}
.hover-light-grey:hover {
    background-color: #ECECEC !important;
}
