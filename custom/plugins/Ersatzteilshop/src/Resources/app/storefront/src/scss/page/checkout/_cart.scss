.is-act-cartpage {
    .checkout {
        margin-top: 20px;
    }
}

.checkout {
    margin-top: 20px;
}

.cart-main-header{
    margin-bottom: 1.8rem;
    padding-top: 35px;
}
.cart-table-header {
    padding: 10px;
    color: #202E3D;
    font-size: 14px;
    font-weight: 900;
    margin-bottom: 0;
}

.card-title.cart-table-header {
    border-bottom: 0;
}
.cart-item-delivery-date {
    .delivery-information {
        //color: $primary;
        //font-weight: 700;
        color: #202E3D;
        font-size: 14px;
        font-weight: 400;
    }
}
.register-submit {
    display: flex;
    gap: 20px;

    @include media-breakpoint-down(sm) {
        display: grid;
        gap: 10px;
    }
}

body.is-ctl-register {
    .register-submit {
        @include media-breakpoint-down(sm) {
            margin-top: 40px;
        }
    }
}

.checkout-register-back {
    float: left;
    display: flex;
    height: 42px;
    min-height: 42px;
    padding: 9px 0px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex: 1 0 0;
    border-radius: 8px;
    background: var(--<PERSON>ra<PERSON>, #E0E0E0);
}
.checkout-register-submit {
    float: right;
    display: flex;
    height: 42px;
    min-height: 42px;
    padding: 9px 0px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    flex: 1 0 0;
    border-radius: 8px;
    color: #202E3D;
    background-color: #74cb7b;
}

.register-option {
    .form-check {
        margin-bottom: 20px;
        display: flex;
        gap: 8px; /* Abstand zwischen Radio-Button und Label */
        align-items: center;
    }
}

.register-option .form-check-input {
    width: 28px; /* Größe des äußeren Kreises */
    height: 28px;
    border-radius: 50%;
    border: 2px solid #d3d3d3; /* Hellgrauer Rand */
    background-color: white;
    appearance: none;
    outline: none;
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 0;
    flex-shrink: 0; /* Verhindert das Verziehen der Checkbox */
}

.register-option .form-check-label {
    flex: 1; /* Label füllt den restlichen Platz */
    &:hover {
        cursor: pointer;
    }

}

.register-option .form-check-input:checked::before {
    content: "";
    width: 14px; /* Größe des inneren Punkts */
    height: 14px;
    background-color: #202E3D;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.register-option {
    .password-wrapper {
        position: relative;
        display: flex;
        align-items: center;
    }

    /* Standard Input - Platzhalter */
    .js-password-input,
    .js-password-input::placeholder {
        font-size: 1rem; /* Standardgröße für Platzhalter und Textfelder */
    }

    /* Platzhalter für Firefox */
    .js-password-input::-moz-placeholder {
        font-size: 1rem;
    }

    /* Platzhalter für Webkit-Browser (z.B. Chrome) */
    .js-password-input::-webkit-input-placeholder {
        font-size: 1rem;
    }

    /* Nur für Passwortfelder: Nur die Punkte werden 24px groß */
    .js-password-input[type="password"] {
        font-size: 24px; /* Nur die Punkte im Passwortfeld werden größer */
    }

    /* Um sicherzustellen, dass der Platzhalter 1rem bleibt, auch im Passwortfeld */
    .js-password-input[type="password"]::placeholder,
    .js-password-input[type="password"]::-webkit-input-placeholder {
        font-size: 1rem; /* Der Platzhalter bleibt bei 1rem */
    }

    .toggle-password {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        //font-size: 18px;
        //color: #777;
    }

    .toggle-password:hover {
        color: #333;
    }

    .password-hint {
        font-size: 14px;
        color: var(--Hover, #202E3D);
        margin-top: 10px;
        margin-bottom: 20px;
        display: block;
        text-align: right;
    }

    .hidden {
        display: none;
    }
}

.advantage-bar {
    background: #eee;
    color: $black;
    text-align: center;

    .advantage-bar-block {
        padding: 45px 30px;
        img {
            height: 45px;
            width: 45px;
        }

        .advantage-bar-text {
            padding-top: 5px;
            font-size: 12px;
        }
    }
}

.warning {
    background-color: #ffffc8;
    margin-bottom: 0;
    padding-bottom: 20px;
}
.cart-add-promotion {
    .form-control {
        //height: calc(1.5em + 1.125rem + 2px);
        height: 48px;
    }
}
.button_spacer {
    margin: 8px 0;
    color: #999;
    font-size: 10px;
}
.button_cart_spacer {
    margin-bottom: -5px;
}
@include media-breakpoint-down(sm) {
    .is-act-cartpage {
        .checkout {
            margin-top: 0px;
        }
    }

    .advantage-bar {
        padding-top: 15px;
        padding-bottom: 15px;
        .advantage-bar-block {
            padding: 25px 0;
        }
    }

    .register-submit {
        text-align: center;
    }

    .checkout-register-back {
        float: none;
        margin-bottom: 10px;
        width: 100%;
    }
    .checkout-register-submit {
        float: none;
        width: 100%;
    }

    .begin-checkout-btn {
        width: 100%;
    }

    .offcanvas-cart-actions {
        .btn-link {
            width: 100%;
        }
    }
}

.shop-review-widget.on-checkout-page {
    margin-top: 10px;

    a {
        color: #000;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        align-content: center;
        flex-wrap: nowrap;
        flex-direction: row;

        & > div {
            margin-right: 0;
        }

        .review-text {
            padding: 3px;

            .stars {
                span {
                    font-size: 13px;
                    line-height: 13px;
                    margin-right: 6px;
                    vertical-align: middle;
                }

                > img {
                    margin-bottom: 0
                }

                .reviewBox {
                    font-weight: bold;
                }
            }

            .rating-img {
                height: 15px;
            }
        }

        .shop-icon {
            padding-top: 2px;
        }
    }
}


@include media-breakpoint-up(xl) {
    .is-ctl-checkout.is-act-cartpage {
        .container-main {
            min-height: 500px;
        }
    }
}

.empty-cart-page-tabs {
    @include media-breakpoint-down(sm) {
        display: block !important;
    }

    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;

    .tab {
        margin-bottom: 40px;
        overflow: hidden;
        flex: 1;

        .tab-head {
            border-radius: 8px;
            border: 2px solid #E0E0E0;
            background: #E0E0E0;
            padding: 9px 0px;
            font-size: 20px;
            display: flex;
            height: 42px;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: inherit;
            cursor: pointer;

            @include media-breakpoint-down(sm) {
                width: 100%;
            }

            span {
                font-weight: 900;
                line-height: 150%;
                font-size: 20px;
            }

            svg {
                margin-right: 10px;
            }
        }
    }
}

.register-account-optional-container {
    margin-bottom: 40px;
    @include media-breakpoint-down(sm) {
        border-radius: 8px;
        border: 1px solid var(--dunkel-grau, #E0E0E0);
        padding: 10px;
        margin-bottom: 0px;
    }
}

.register-right-container {
    @include media-breakpoint-down(sm) {
        padding-left: 5px !important;

        .register-address {
            border-radius: 8px;
            border: 1px solid var(--dunkel-grau, #E0E0E0);
            padding: 10px 10px 0px 10px;
            margin-bottom: 20px
        }
    }
}

.register-personal {
    @include media-breakpoint-down(sm) {
        margin-bottom: 20px;
    }
}

.prio-container {
    display: grid;
    grid-gap: 20px 10px;
    grid-template-columns: 1fr min-content min-content;
    grid-template-areas: "checkbox price arrow" "body body body";
    grid-auto-rows: auto;
    border-radius: 8px;
    border: 1px solid #74CB7B;
    align-items: center;
}

.prio-label {
    color: #202E3D;
    leading-trim: both;
    text-edge: cap;
    font-size: 14px;
    font-style: normal;
    font-weight: 900;
    line-height: 150%; /* 21px */
    text-wrap: nowrap;
}

.prio-help {
    &:hover {
        text-decoration: none;
    }
}

.panel {
    .panel-body.prio-panel-body {
        padding: 0;
        display: flex;
        gap: 20px;
        grid-area: body;

        .prio-panel-image {
            padding: 4.601px 9px 4.879px 9px;
            border-radius: 5.76px;
            border: 0.72px solid var(--dunkel-grau, #E0E0E0);
        }
    }

    .panel-head.prio-panel-head {
        border: none !important;
        background: none !important;
    }

    &.hide {
        .panel-head.prio-panel-head {
            filter: opacity(0.5);
        }

        .prio-container {
            grid-gap: 0 10px;
        }
    }
}

.register-billingaddress-container {
    @include media-breakpoint-down(sm) {
        border-radius: 8px;
        border: 1px solid var(--dunkel-grau, #E0E0E0);
        padding: 10px;
    }
}

.register-card {
    .form-group {
        @include media-breakpoint-down(sm) {
            margin-bottom: 10px;
        }
    }
}

.register-zipcode-field {
    padding-right: 15px !important;
    @include media-breakpoint-down(sm) {
        padding-right: 5px !important;
    }
}

@media screen and (max-width: 768px) {
    .k11-checkbox-container .prio-description {
        display: none;
    }
}

@media screen and (min-width: 769px) {
    .panel-body .prio-description {
        display: none;
    }
}

.is-ctl-checkout.is-act-confirmpage {
    .cart-item-product {
        border-radius: 8px;
        border: 1px solid #D8D8D8;
        padding: 20px;
        margin-top: 20px;
    }

    .cart-item-promotion {
        border-radius: 8px;
        border: 1px solid #D8D8D8;
        padding: 20px;
        margin-top: 20px;
    }

    .cart-item-remove .btn-light {
        background-color: #fff;
        border: none;
        color: #1C1B1F;
    }

    .cart-item-tax-price {
        padding-top: 10px;
        padding-right: 30px;
    }

    .cart-item-total-price {
        padding-top: 10px;
        padding-right: 30px;
    }

    .cart-item-unit-price {
        padding-top: 10px;
        padding-right: 25px;
        @media (max-width: 575px) {
            padding-right: 30px;
        }
    }

    .cart-item:last-child {
        border-bottom: 1px solid #D8D8D8 !important;
    }
}
